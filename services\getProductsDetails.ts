import { ProductDetails } from "@/types/Product";
import useSWR from "swr";

// API response type from the product details route
type ApiProductDetails = {
  product_id: number;
  name: string;
  is_shared: boolean;
  default_measurement: {
    measurement_id: number;
    name: string;
    unit: "g" | "ml";
    kcal: number;
    protein: number;
    carbs: number;
    fat: number;
  };
};

const fetcher = async (url: string): Promise<ProductDetails> => {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error("Failed to fetch product details");
  }
  const apiProduct: ApiProductDetails = await res.json();
  console.log('apiProduct', apiProduct);
  // Map API response to frontend type
  return {
    product_id: apiProduct.product_id.toString(),
    name: apiProduct.name,
    is_shared: apiProduct.is_shared,
    default_measurement: {
      name: apiProduct.default_measurement.name,
      unit: apiProduct.default_measurement.unit,
      kcal: apiProduct.default_measurement.kcal,
      protein: apiProduct.default_measurement.protein,
      carbs: apiProduct.default_measurement.carbs,
      fat: apiProduct.default_measurement.fat,
    },
  };
};

export function getProductDetails(productId: string) {
  const url = `/api/product/${productId}`;

  const { data, error, isLoading, mutate } = useSWR<ProductDetails>(
    productId ? url : null,
    fetcher
  );
  console.log('data', data);
  return {
    product: data,
    isLoading,
    isError: error,
    refetch: mutate,
  };
}
