import useSWR from "swr";

// API response type from the user details route
type ApiUserDetails = {
  display_name: string | null;
  email: string | null;
};

// Frontend type for user details
export interface UserDetails {
  display_name: string | null;
  email: string | null;
}

const fetcher = async (url: string): Promise<UserDetails> => {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error("Failed to fetch user details");
  }
  const apiUserDetails: ApiUserDetails = await res.json();
  console.log('apiUserDetails', apiUserDetails);

  // Map API response to frontend type (in this case, they're the same)
  return {
    display_name: apiUserDetails.display_name,
    email: apiUserDetails.email,
  };
};

export function getUserDetails() {
  const url = `/api/userDetails`;

  const { data, error, isLoading, mutate } = useSWR<UserDetails>(
    url,
    fetcher
  );
  console.log('user details data', data);

  return {
    userDetails: data || null,
    isLoading,
    isError: error,
    refetch: mutate,
  };
}