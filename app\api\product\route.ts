import { ProductFormData } from "@/types/Measurement";
import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";
import { abort } from "process";

export async function POST(request: NextRequest) {
  const body: ProductFormData = await request.json();
  console.log("body", body);
  body.measurements.push({
    name: "100g",
    quantity: 100,
    unit: body.mainUnit,
    kcal: body.calories,
    protein: body.protein,
    carbs: body.carbs,
    fat: body.fat,
    is_default: true,
  });
  try {
    const supabase = await createClient();
    const { data: user } = await supabase.auth.getUser();
    if (!user?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data, error } = await supabase.rpc('insert_product_with_measurements', {
      p_name: body.name,
      p_is_shared: body.isShared || false,
      p_measurements: body.measurements
    });

    console.log(data);
    console.log(error);
    // const { data, error } = await supabase
    //   .from("products")
    //   .insert({
    //     name: body.name,
    //     is_shared: body.isShared,
    //     created_by: user.user.id,
    //     measurements: body.measurements,
    //   })
    //   .select("product_id");
    // if (error) {
    //   return NextResponse.json(
    //     { error: "Failed to create product" },
    //     { status: 500 }
    //   );
    // }

    // const productId = data[0].product_id;


    // body.measurements.forEach(async (measurement) => {
    //   const { error } = await supabase.from("measurements").insert({
    //     name: measurement.name,
    //     quantity: measurement.quantity,
    //     unit: measurement.unit,
    //     kcal: measurement.kcal,
    //     protein: measurement.protein,
    //     carbs: measurement.carbs,
    //     fat: measurement.fat,
    //     is_default: measurement.is_default,
    //     product_id: productId,
    //   });
    //   if (error) {
    //     throw error;
    //   }
    // });


    return NextResponse.json({ message: "Product created" });
  } catch (err) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
