"use client";

import * as React from "react";
import { EditIcon, TrashIcon } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Product } from "@/types/Product";
import Link from "next/link";


interface ProductTableProps {
  products: Product[];
  onEdit?: (productId: string) => void;
  onDelete?: (productId: string) => void;
  className?: string;
}

export function ProductTable({
  products,
  onEdit,
  onDelete,
  className,
}: ProductTableProps) {
  // Placeholder functions for actions
  const handleEdit = (productId: string) => {
    onEdit?.(productId);
  };

  const handleDelete = (productId: string) => {
    onDelete?.(productId);
  };

  return (
    <div className={`w-full flex-1 overflow-auto p-4 bg-background ${className || ""}`}>
      <div className="rounded-lg  border overflow-hidden">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead>Product Name</TableHead>
              <TableHead>Calories</TableHead>
              <TableHead>Protein</TableHead>
              <TableHead>Carbs</TableHead>
              <TableHead>Fat</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center py-8 text-muted-foreground"
                >
                  No products found
                </TableCell>
              </TableRow>
            ) : (
              products.map((product) => (
                <TableRow key={product.product_id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <Link href={`/product/${product.product_id}`}>{product.name}</Link>
                      {product.is_shared && (
                        <span className="text-xs text-muted-foreground">
                          Produkt udostępniony
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{product.calories} kcal/100g</TableCell>
                  <TableCell>{product.protein}g</TableCell>
                  <TableCell>{product.carbs}g</TableCell>
                  <TableCell>{product.fat}g</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(product.product_id)}
                        className="h-8 w-8 cursor-pointer"
                        disabled={product.is_shared}
                      >
                        <EditIcon className="h-4 w-4" />
                        <span className="sr-only">Edit {product.name}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(product.product_id)}
                        className="h-8 w-8 cursor-pointer text-destructive hover:text-destructive"
                        disabled={product.is_shared}
                      >
                        <TrashIcon className="h-4 w-4" />
                        <span className="sr-only">Delete {product.name}</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// Placeholder utility functions for backend implementation
export const productTableUtils = {
  // Function to edit a product
  editProduct: async (product: Product): Promise<Product> => {
    // TODO: Implement backend edit
    console.log("Editing product:", product);
    return product;
  },

  // Function to delete a product
  deleteProduct: async (productId: string): Promise<boolean> => {
    // TODO: Implement backend delete
    console.log("Deleting product:", productId);
    return true;
  },

  // Function to get product details
  getProduct: async (productId: string): Promise<Product | null> => {
    // TODO: Implement backend get product
    console.log("Getting product:", productId);
    return null;
  },

  // Function to update product
  updateProduct: async (
    productId: string,
    updates: Partial<Product>
  ): Promise<Product> => {
    // TODO: Implement backend update
    console.log("Updating product:", productId, updates);
    return {} as Product;
  },
};

export type { Product, ProductTableProps };
