"use client";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import PageHeader from "@/components/general/PageHeader";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ProductFilter } from "@/components/product/ProductFilter";
import { useState } from "react";
import { ProductTable } from "@/components/product/ProductTable";
import { PaginationComponent } from "@/components/general/PaginationComponent";
import { ProductMobileList } from "@/components/product/ProductMobileList";
import { useIsMobile } from "@/hooks/use-mobile";
import { ProductAddEditPopup } from "@/components/product/ProductAddEditPopup";
import { ProductFormData } from "@/types/Measurement";
import { getProductsList } from "@/services/getProductsList";
import { addProduct } from "@/services/addProduct";

export default function ProductPage() {
  const { products, count, isLoading, isError, refetch } = getProductsList();
  const isMobile = useIsMobile();
  const [isAddPopupOpen, setIsAddPopupOpen] = useState(false);
  const [productId, setProductIdToEdit] = useState<string | null>(null);

  const handleOpenChange = (open: boolean) => {
    setIsAddPopupOpen(open);
    if (!open) {
      setProductIdToEdit(null);
    }
  };

  const handleAddProduct = async (data: ProductFormData) => {
    try {
      await addProduct(data);
      refetch();
    } catch (err) {
      console.error("Failed to add product:", err);
    }
  };

  const handleEditProduct = (productId: string) => {
    setIsAddPopupOpen(true);
    setProductIdToEdit(productId);
  };

  return (
    <PageWrapper>
      <PageHeader
        title="Produkty"
        actions={
          <Button onClick={() => setIsAddPopupOpen(true)}>
            <Plus className="w-4 h-4" />
            Dodaj produkt
          </Button>
        }
      />
      <ProductFilter onSearch={refetch} onSortChange={refetch} />
      {isMobile ? (
        <ProductMobileList products={products} />
      ) : (
        <ProductTable products={products} onEdit={handleEditProduct} />
      )}
      <PaginationComponent
        pageSize={10}
        totalItems={count}
        onPageChange={refetch}
      />

      <ProductAddEditPopup
        open={isAddPopupOpen}
        onOpenChange={handleOpenChange}
        onSubmit={handleAddProduct}
        mode={productId ? "edit" : "add"}
        productId={productId}
      />
    </PageWrapper>
  );
}
