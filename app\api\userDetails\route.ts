import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: user } = await supabase.auth.getUser();
    if (!user?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const { data: userDetails, error } = await supabase
      .from("user_extended")
      .select("display_name, email")
      .eq("user_id", user.user.id)
      .single();
    if (error) {
      return NextResponse.json(
        { error: "Failed to fetch user details" },
        { status: 500 }
      );
    }
    return NextResponse.json(userDetails);
  } catch (err) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
