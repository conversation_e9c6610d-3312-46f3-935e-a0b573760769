import { Measurement } from "@/types/Measurement";
import { useSearchParams } from "next/navigation";
import useSWR from "swr";

// API response type from the measurements route
type ApiMeasurement = {
  measurement_id: number;
  name: string;
  quantity: number;
  unit: "g" | "ml";
  kcal: number;
  protein: number;
  carbs: number;
  fat: number;
  is_default: boolean;
};

type ApiMeasurementsResponse = {
  measurements: ApiMeasurement[];
  count: number;
};

interface MeasurementsResponse {
  measurements: Measurement[];
  count: number;
}

const fetcher = async (url: string): Promise<MeasurementsResponse> => {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error("Failed to fetch measurements");
  }
  const apiResponse: ApiMeasurementsResponse = await res.json();

  // Map API response to frontend type
  return { measurements: apiResponse.measurements.map((measurement) => ({
    measurement_id: measurement.measurement_id.toString(),
    name: measurement.name,
    quantity: measurement.quantity,
    unit: measurement.unit,
    kcal: measurement.kcal,
    protein: measurement.protein,
    carbs: measurement.carbs,
    fat: measurement.fat,
    is_default: measurement.is_default,
  })), count: apiResponse.count };
};

export function getMeasurementsByProductId(productId: string) {
  const searchParams = useSearchParams();
  const page = parseInt(searchParams.get("page") || "1");
  const url = `/api/measurement/${productId}?page=${page}`;

  const { data, error, isLoading, mutate } = useSWR<MeasurementsResponse>(
    productId ? url : null,
    fetcher
  );

  return {
    measurements: data?.measurements || [],
    count: data?.count || 0,
    isLoading,
    isError: error,
    refetch: mutate,
  };
}
