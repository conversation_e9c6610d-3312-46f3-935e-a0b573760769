import { ProductFormData } from "@/types/Measurement";

export async function addProduct(data: ProductFormData) {
  try {
    const res = await fetch("/api/product", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!res.ok) {
      throw new Error("Failed to add product");
    }

    return await res.json();
  } catch (err) {
    console.error("Failed to add product:", err);
    throw err;
  }
}
